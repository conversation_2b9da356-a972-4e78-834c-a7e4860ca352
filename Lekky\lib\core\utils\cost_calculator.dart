// File: lib/core/utils/cost_calculator.dart

import '../models/meter_entry.dart';
import 'average_calculator.dart';
import 'date_time_utils.dart';
import 'logger.dart';

/// Utility class for calculating electricity costs for user-defined time periods
class CostCalculator {
  /// Calculates the cost of electricity for a user-defined time period
  ///
  /// Takes a list of meter entries and a date range, and returns the cost
  /// for that period based on the specified algorithm.
  static double calculateCost(
      List<MeterEntry> entries, DateTime fromDate, DateTime toDate) {
    // Step 1: Validate the date range
    final validationResult = _validateDateRange(entries, fromDate, toDate);
    if (validationResult != null) {
      Logger.error('Cost calculation validation failed: $validationResult');
      return 0.0;
    }

    // Step 2: Prepare the data
    final sortedEntries = _prepareData(entries);

    // Get meter readings only (where amountToppedUp = 0)
    final meterReadings =
        sortedEntries.where((entry) => entry.amountToppedUp == 0).toList();

    // Get the latest meter reading timestamp
    final latestMeterReadingTimestamp = meterReadings.isNotEmpty
        ? meterReadings.last.timestamp
        : DateTime.now();

    // Step 3: Handle future periods
    if (fromDate.isAfter(latestMeterReadingTimestamp) ||
        fromDate.isAtSameMomentAs(latestMeterReadingTimestamp)) {
      return _handleFuturePeriod(sortedEntries, fromDate, toDate);
    }

    // Step 4: Build usage intervals and calculate cost

    // Find MR1: The latest meter reading with timestamp <= fromDate
    final mr1 = _findLatestMeterReadingBeforeOrAt(meterReadings, fromDate);
    if (mr1 == null) {
      // This shouldn't happen due to validation, but as a fallback
      return _handleFuturePeriod(sortedEntries, fromDate, toDate);
    }

    // Find MR2: The latest meter reading with timestamp <= toDate
    final mr2 = _findLatestMeterReadingBeforeOrAt(meterReadings, toDate);
    if (mr2 == null) {
      // This shouldn't happen due to validation, but as a fallback
      return _handleFuturePeriod(sortedEntries, fromDate, toDate);
    }

    // Calculate the components of the cost
    double startValue = 0.0;
    double middleValue = 0.0;
    double endValue = 0.0;

    // Calculate start value
    if (fromDate.isAtSameMomentAs(mr1.timestamp)) {
      startValue = 0.0;
    } else {
      startValue = _calculateStartValue(sortedEntries, mr1, fromDate);
    }

    // Calculate middle value
    middleValue =
        _calculateMiddleValue(sortedEntries, mr1, mr2, fromDate, toDate);

    // Calculate end value
    if (toDate.isAtSameMomentAs(mr2.timestamp)) {
      endValue = 0.0;
    } else {
      endValue = _calculateEndValue(
          sortedEntries, mr2, toDate, latestMeterReadingTimestamp);
    }

    // Sum the components
    double cost = startValue + middleValue + endValue;

    // Ensure cost is not negative
    return cost > 0 ? cost : 0.0;
  }

  /// Validates the date range against constraints
  static String? _validateDateRange(
      List<MeterEntry> entries, DateTime fromDate, DateTime toDate) {
    // Empty entries check
    if (entries.isEmpty) {
      return "No meter entries available";
    }

    // Sort entries
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Date Order Check
    if (toDate.isBefore(fromDate)) {
      return "To date cannot be before from date";
    }

    // Total Average Check
    final totalAverage = _calculateTotalAverage(sortedEntries);
    if (totalAverage <= 0) {
      return "Cannot calculate cost with zero or negative average usage";
    }

    // Data Validity Check
    if (_hasInvalidEntries(sortedEntries)) {
      return "History table contains invalid entries";
    }

    return null;
  }

  /// Checks if there are any invalid entries in the list
  static bool _hasInvalidEntries(List<MeterEntry> entries) {
    // Get only meter readings, excluding dismissal entries
    final meterReadings = entries.where((e) => e.typeCode == 0).toList();
    if (meterReadings.length < 2) return false;

    for (int i = 1; i < meterReadings.length; i++) {
      final prev = meterReadings[i - 1];
      final curr = meterReadings[i];

      // Sum top-ups between readings
      double sumTopUps = 0.0;
      for (final entry in entries) {
        if (entry.amountToppedUp > 0 &&
            entry.timestamp.isAfter(prev.timestamp) &&
            entry.timestamp.isBefore(curr.timestamp)) {
          sumTopUps += entry.amountToppedUp;
        }
      }

      // Check if current reading is greater than previous reading plus top-ups
      if (curr.reading > prev.reading + sumTopUps) {
        return true;
      }
    }

    return false;
  }

  /// Prepares the data for cost calculation by calculating short averages
  static List<MeterEntry> _prepareData(List<MeterEntry> entries) {
    // Create a copy of the entries list to avoid modifying the original
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Calculate short averages for each meter reading
    for (int i = 0; i < sortedEntries.length; i++) {
      if (sortedEntries[i].amountToppedUp == 0) {
        // Find previous meter reading
        int prevIndex = -1;
        for (int j = i - 1; j >= 0; j--) {
          if (sortedEntries[j].amountToppedUp == 0) {
            prevIndex = j;
            break;
          }
        }

        if (prevIndex >= 0) {
          // Calculate short average
          final prev = sortedEntries[prevIndex];
          final curr = sortedEntries[i];
          final days = curr.timestamp.difference(prev.timestamp).inDays;

          if (days > 0) {
            // Sum top-ups between readings
            double sumTopUps = 0.0;
            for (int j = prevIndex + 1; j < i; j++) {
              sumTopUps += sortedEntries[j].amountToppedUp;
            }

            final usage = prev.reading - curr.reading + sumTopUps;
            final shortAverage = usage > 0 ? usage / days : 0.0;

            // Update the entry with the calculated short average
            sortedEntries[i] = sortedEntries[i].copyWith(
              shortAverageAfterTopUp: shortAverage,
            );
          }
        }
      }
    }

    return sortedEntries;
  }

  /// Handles future periods (when fromDate is on or after the latest meter reading)
  static double _handleFuturePeriod(
      List<MeterEntry> entries, DateTime fromDate, DateTime toDate) {
    // Calculate total average
    final totalAverage = _calculateTotalAverage(entries);

    // Calculate days in the range (inclusive) and round up to the nearest whole day
    final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);

    // Compute cost
    return totalAverage * days;
  }

  /// Finds the latest meter reading with timestamp <= targetDate
  static MeterEntry? _findLatestMeterReadingBeforeOrAt(
      List<MeterEntry> meterReadings, DateTime targetDate) {
    // Sort readings by timestamp (descending)
    final sortedReadings = List<MeterEntry>.from(meterReadings)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Find the first reading with timestamp <= targetDate
    for (final reading in sortedReadings) {
      if (reading.timestamp.isBefore(targetDate) ||
          reading.timestamp.isAtSameMomentAs(targetDate)) {
        return reading;
      }
    }

    return null;
  }

  /// Calculates the start value component of the cost
  static double _calculateStartValue(
      List<MeterEntry> entries, MeterEntry mr1, DateTime fromDate) {
    // Find the next meter reading after mr1
    final meterReadings = entries.where((e) => e.amountToppedUp == 0).toList();
    int mr1Index = meterReadings.indexOf(mr1);

    if (mr1Index >= meterReadings.length - 1) {
      // mr1 is the last meter reading, use total average
      final totalAverage = _calculateTotalAverage(entries);
      final days =
          DateTimeUtils.calculateDaysWithPrecision(mr1.timestamp, fromDate);
      return totalAverage * days;
    }

    // Get the next meter reading
    final mr1Next = meterReadings[mr1Index + 1];

    // Get short average for the next reading
    final shortAverage = mr1Next.shortAverageAfterTopUp ?? 0.0;

    // Calculate days between mr1 and fromDate with precision
    final days =
        DateTimeUtils.calculateDaysWithPrecision(mr1.timestamp, fromDate);

    // Multiply days by short average
    return shortAverage * days;
  }

  /// Calculates the middle value component of the cost
  static double _calculateMiddleValue(List<MeterEntry> entries, MeterEntry mr1,
      MeterEntry mr2, DateTime fromDate, DateTime toDate) {
    // If mr1 and mr2 are the same, there's no middle value
    if (mr1.id == mr2.id) {
      return 0.0;
    }

    // Get all meter readings between mr1 and mr2
    final meterReadings = entries
        .where((e) => e.amountToppedUp == 0)
        .where((e) =>
            e.timestamp.isAfter(mr1.timestamp) &&
                e.timestamp.isBefore(mr2.timestamp) ||
            e.timestamp.isAtSameMomentAs(mr2.timestamp))
        .toList();

    // If there are no readings between, return 0
    if (meterReadings.isEmpty) {
      return 0.0;
    }

    double middleValue = 0.0;

    // For each reading, calculate the cost for that interval
    for (final reading in meterReadings) {
      final shortAverage = reading.shortAverageAfterTopUp ?? 0.0;

      // Find the previous reading
      final prevReadingIndex = entries.indexOf(reading) - 1;
      if (prevReadingIndex < 0) continue;

      MeterEntry? prevReading;
      for (int i = prevReadingIndex; i >= 0; i--) {
        if (entries[i].amountToppedUp == 0) {
          prevReading = entries[i];
          break;
        }
      }

      if (prevReading == null) continue;

      // Calculate days in this interval with precision
      final intervalStart = prevReading.timestamp.isAfter(fromDate)
          ? prevReading.timestamp
          : fromDate;
      final intervalEnd =
          reading.timestamp.isBefore(toDate) ? reading.timestamp : toDate;

      final days =
          DateTimeUtils.calculateDaysWithPrecision(intervalStart, intervalEnd);

      // Add to middle value
      middleValue += shortAverage * days;
    }

    return middleValue;
  }

  /// Calculates the end value component of the cost
  static double _calculateEndValue(List<MeterEntry> entries, MeterEntry mr2,
      DateTime toDate, DateTime latestMeterReadingTimestamp) {
    // If toDate is after the latest meter reading, use total average
    if (toDate.isAfter(latestMeterReadingTimestamp)) {
      final totalAverage = _calculateTotalAverage(entries);
      final days =
          DateTimeUtils.calculateDaysWithPrecision(mr2.timestamp, toDate);
      return totalAverage * days;
    }

    // Find the next meter reading after mr2
    final meterReadings = entries.where((e) => e.amountToppedUp == 0).toList();
    int mr2Index = meterReadings.indexOf(mr2);

    if (mr2Index >= meterReadings.length - 1) {
      // mr2 is the last meter reading, use total average
      final totalAverage = _calculateTotalAverage(entries);
      final days =
          DateTimeUtils.calculateDaysWithPrecision(mr2.timestamp, toDate);
      return totalAverage * days;
    }

    // Get the next meter reading
    final mr2Next = meterReadings[mr2Index + 1];

    // Get short average for the next reading
    final shortAverage = mr2Next.shortAverageAfterTopUp ?? 0.0;

    // Calculate days between mr2 and toDate with precision
    final days =
        DateTimeUtils.calculateDaysWithPrecision(mr2.timestamp, toDate);

    // Multiply days by short average
    return shortAverage * days;
  }

  /// Calculates the total average usage rate from all entries
  static double _calculateTotalAverage(List<MeterEntry> entries) {
    return AverageCalculator.calculateTotalAverage(entries);
  }
}
