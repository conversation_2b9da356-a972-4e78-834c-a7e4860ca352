// File: lib/core/shared/enums/entry_enums.dart

/// Filter type for history entries
enum EntryFilterType {
  /// Show all entries
  all,

  /// Show only meter readings
  meterReadings,

  /// Show only top-ups
  topUps,

  /// Show only invalid entries
  invalid,
}

/// Sort order for history entries
enum EntrySortOrder {
  /// Newest first
  newestFirst,

  /// Oldest first
  oldestFirst,
}

/// Status levels for entries
enum EntryStatus {
  /// Entry is valid
  valid(0),

  /// Entry is invalid
  invalid(1),

  /// Entry is ignored (user dismissed validation issue)
  ignored(2);

  const EntryStatus(this.value);

  /// Database value for this status
  final int value;

  /// Create status from database value
  static EntryStatus fromValue(int value) {
    switch (value) {
      case 0:
        return EntryStatus.valid;
      case 1:
        return EntryStatus.invalid;
      case 2:
        return EntryStatus.ignored;
      default:
        return EntryStatus.valid;
    }
  }
}
