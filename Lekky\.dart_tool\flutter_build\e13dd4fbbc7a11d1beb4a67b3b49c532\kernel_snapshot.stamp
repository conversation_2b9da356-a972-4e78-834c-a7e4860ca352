{"inputs": ["D:\\000.Workspace\\Lekky\\.dart_tool\\flutter_build\\e13dd4fbbc7a11d1beb4a67b3b49c532\\native_assets.yaml", "D:\\000.Workspace\\Lekky\\.dart_tool\\package_config_subset", "C:\\src\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "D:\\000.Workspace\\Lekky\\lib\\main.dart", "D:\\000.Workspace\\Lekky\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\material.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\flutter_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\flutter_riverpod.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\data\\latest.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\constants\\app_constants.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\di\\service_locator.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\localization\\app_localizations.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\riverpod_localization_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\settings_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\theme_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\models\\theme_mode.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\theme\\app_theme.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\theme\\app_colors.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\splash\\presentation\\screens\\simplified_splash_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\screens\\setup_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\presentation\\screens\\welcome_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\settings_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\presentation\\widgets\\reactive_reminder_listener.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\widgets\\reactive_alert_listener.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\screens\\notification_debug_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\csv_export_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\csv_import_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\about_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\donate_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\appearance_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\region_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\language_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\currency_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\date_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\update_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\alert_threshold_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\days_advance_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\notification_types_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\reminders_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\notification_utilities_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\tips_tricks_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\delete_all_data_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\date_format_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\time_display_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\screens\\theme_mode_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\screens\\riverpod_dashboard_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\screens\\riverpod_history_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\data\\notification_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\screens\\riverpod_validation_dashboard_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\screens\\cost_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\background_monitoring_service.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\unified_alert_manager.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\constants\\currency_constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.4\\lib\\path_provider_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.2.2\\lib\\shared_preferences_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\sqflite.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.2\\lib\\url_launcher_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\webview_flutter_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.3\\lib\\shared_preferences_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.2\\lib\\url_launcher_ios.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\webview_flutter_wkwebview.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\device_info_plus.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\flutter_local_notifications_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\package_info_plus.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\url_launcher_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\url_launcher_macos.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.3\\lib\\url_launcher_windows.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\feedback.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\flutter_logo.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\toggleable.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\widgets.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\cupertino_localizations.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_cupertino_localizations.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_material_localizations.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_widgets_localizations.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\material_localizations.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\widgets_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\riverpod.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\change_notifier_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\consumer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\framework.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\src\\env.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\src\\exceptions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\flutter_local_notifications.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_it-7.7.0\\lib\\get_it.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\database\\database_helper.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\logger.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\meter_readings\\domain\\repositories\\meter_reading_repository.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\meter_readings\\data\\repositories\\meter_reading_repository_impl.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\top_ups\\domain\\repositories\\top_up_repository.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\top_ups\\data\\repositories\\top_up_repository_impl.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\controllers\\home_controller.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\controllers\\entry_controller.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\domain\\repositories\\notification_repository.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\data\\notification_repository_impl.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\services\\data_integrity_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\services\\validation_trigger_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\services\\meter_reading_validator.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\controllers\\validation_dashboard_controller.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\data\\cost_repository.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\domain\\services\\hybrid_cost_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\averages\\domain\\repositories\\average_repository.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\averages\\data\\repositories\\average_repository_impl.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\averages\\domain\\repositories\\per_reading_average_repository.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\averages\\data\\repositories\\per_reading_average_repository_impl.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\averages\\domain\\services\\average_service.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod_annotation-2.6.1\\lib\\riverpod_annotation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.2.3\\lib\\shared_preferences.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\constants\\preference_keys.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\localization_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\riverpod_localization_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\models\\date_format.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\settings_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\localization_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\event_bus.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\domain\\models\\setup_preferences.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\meter_readings\\domain\\models\\meter_reading.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\providers\\dashboard_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\database_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\settings_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\theme_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\theme_provider.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\services.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\theme\\lekky_colors.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\domain\\models\\notification.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\preference_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\splash\\presentation\\widgets\\simplified_splash_animation.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\date_settings_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\alert_settings_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\meter_reading_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\region_settings_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\appearance_settings_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\presentation\\providers\\welcome_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\presentation\\widgets\\feature_item.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\presentation\\widgets\\welcome_buttons.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\widgets\\app_banner.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\domain\\models\\settings_category.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\domain\\models\\settings_sub_branch.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\widgets\\expandable_settings_category.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\domain\\services\\reminder_settings_watcher.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\domain\\services\\foreground_resume_handler.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\domain\\models\\reminder_state.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\domain\\models\\reminder_error.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\presentation\\providers\\reminder_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\presentation\\providers\\alert_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\providers\\validation_alert_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\providers\\notification_manager_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\notification_permission_manager.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\domain\\models\\dashboard_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\notification_debug_helper.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\providers\\export_data_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\theme\\app_text_styles.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\widgets\\app_card.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\progress_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared_modules\\data_import_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\backup\\presentation\\screens\\import_results_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\widgets\\currency_input_field.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\data\\paypal_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\radio_option.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\app_update_service.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\widgets\\settings_section_header.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\widgets\\integer_input_field.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\intl.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\reminder_scheduling_service.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\date_formatter.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\widgets\\lekky_button.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\presentation\\providers\\alert_state_manager_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\data_deletion_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\dialogs\\delete_all_data_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\widgets\\settings_radio.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\widgets\\meter_status_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\widgets\\usage_statistics_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\widgets\\top_up_statistics_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\widgets\\quick_actions_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\widgets\\recent_activity_card.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\widgets\\message_banner.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\dialogs\\riverpod_add_entry_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\dialogs\\validation_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\widgets\\validation_icon_widget.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\widgets\\notification_badge.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\providers\\notification_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\dialogs\\notification_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\providers\\history_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\widgets\\history_pagination.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\widgets\\history_entry_item.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\widgets\\history_empty_state.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\dialogs\\history_filter_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\dialogs\\add_entry_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\domain\\models\\history_state.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\timezone.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\top_ups\\domain\\models\\top_up.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\models\\integrity_report.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\models\\validation_issue.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\providers\\validation_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\presentation\\widgets\\issue_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\dialogs\\edit_entry_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\screens\\riverpod_cost_screen.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\workmanager-0.5.2\\lib\\workmanager.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\average_calculator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.4\\lib\\messages.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.2.2\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\compat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\sqflite_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\sqflite_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\utils\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\sqlite_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\sql.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\sqflite.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\factory_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\sqflite_plugin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\url_launcher_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.2\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_webview_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_webview_cookie_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_webview_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.3\\lib\\src\\shared_preferences_async_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.3\\lib\\src\\shared_preferences_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.2\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\webkit_webview_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\webkit_webview_cookie_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\webkit_webview_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\lib\\device_info_plus_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\android_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\ios_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\linux_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\macos_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\web_browser_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\windows_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\device_info_plus_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\device_info_plus_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\flutter_local_notifications.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\capabilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\icon.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\initialization_settings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\location.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\notification_details.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\sound.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\timeout.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\lib\\package_info_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\src\\package_info_plus_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\src\\package_info_plus_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\local.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.3\\lib\\src\\messages.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\scheduler.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\cupertino.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\rendering.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\animation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\gestures.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\material_color_utilities.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\painting.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\semantics.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\characters.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_varied_extent_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\utils\\date_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\state_notifier-0.7.2+1\\lib\\state_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\common.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\future_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_notifier_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.11.0\\lib\\meta.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\internals.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\builders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\change_notifier_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\change_notifier_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\lib\\src\\internals.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\src\\location.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\src\\location_database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\src\\tzdb.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\lib\\flutter_local_notifications_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\flutter_local_notifications_plugin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\initialization_settings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\notification_details.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_flutter_local_notifications.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\bitmap.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\icon.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\initialization_settings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\message.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\notification_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\notification_channel_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\notification_details.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\notification_sound.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\person.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\schedule_mode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\big_picture_style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\big_text_style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\default_style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\inbox_style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\media_style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\messaging_style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\styles\\style_information.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\initialization_settings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\interruption_level.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_action.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_action_option.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_attachment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_category.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_category_option.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_details.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\notification_enabled_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\ios\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\typedefs.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\async.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\collection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_it-7.7.0\\lib\\get_it_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.4\\lib\\path_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\constants\\database_constants.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\bulk_operation_context.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\enums\\entry_enums.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\services\\validation_notification_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\utils\\validation_message_builder.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\domain\\models\\cost_period.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\domain\\models\\cost_result.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\models\\chart_data.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\date_time_utils.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\average.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\per_reading_average.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\meter_entry.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\performance_monitor.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\average_calculation_lock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod_annotation-2.6.1\\lib\\src\\riverpod_annotation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\lib\\freezed_annotation.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\localization_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\localization_state.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\settings_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\settings_state.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\error_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\home\\presentation\\providers\\dashboard_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\database_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\theme_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\theme_state.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\preference_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\preference_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\setup_section_header.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\setup\\presentation\\widgets\\info_notice.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\shared\\widgets\\theme_selector.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\domain\\models\\welcome_state.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\presentation\\providers\\welcome_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\settings\\presentation\\widgets\\settings_sub_branch_item.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\domain\\services\\background_reminder_flags.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\reminders\\domain\\models\\reminder_event.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\reminder_calculator.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\reminder_content_generator.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\domain\\models\\alert_state.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\validation\\domain\\models\\validation_alert_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\smart_notification_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\providers\\notification_evaluator_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\providers\\notification_manager_provider.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.3.1\\lib\\permission_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\file_picker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\csv.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\media_store_plus.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\permission_helper.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\platform_service.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\progress_provider.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\backup\\backup_service.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\backup\\presentation\\dialogs\\import_strategy_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\data_management\\data\\csv_parser.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\data_management\\domain\\import_validator.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\data_management\\presentation\\screens\\import_screen.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\app_error.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\error_types.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_paypal-0.2.0\\lib\\flutter_paypal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\global_state.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_format.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\plural_rules.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\bidi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\bidi_formatter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\micro_money.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\number_format.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\text_direction.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\domain\\models\\alert_state_manager_state.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\presentation\\providers\\alert_state_manager_provider.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\legacy_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_uri.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\responsive_text_utils.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\battery_icon_utils.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\date_formatter_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\marquee-2.2.3\\lib\\marquee.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\providers\\entry_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\widgets\\entry_type_selector.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\domain\\models\\notification_state.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\providers\\notification_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\widgets\\notification_item.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\presentation\\providers\\history_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\presentation\\dialogs\\delete_confirmation_dialog.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\widgets\\date_range_selector_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\history\\domain\\models\\history_state.freezed.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timezone-0.9.4\\lib\\src\\date_time.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\providers\\cost_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\widgets\\cost_summary_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\widgets\\recent_average_chart_card.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\domain\\models\\cost_state.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\workmanager-0.5.2\\lib\\src\\options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\workmanager-0.5.2\\lib\\src\\workmanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\compat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\sqflite_import.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\sqlite_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\services_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\utils\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\sql_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\sqflite_database_factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\exception_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\mixin\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.3.3+1\\lib\\src\\dev_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\url_launcher_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\webview_flutter_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_proxy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_webview.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_webview_api_impls.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\instance_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\platform_views_service_proxy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\weak_reference_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.3\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\common\\instance_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\common\\weak_reference_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\foundation\\foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\ui_kit\\ui_kit.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\web_kit\\web_kit.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\webkit_proxy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\lib\\method_channel\\method_channel_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\lib\\model\\base_device_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\ffi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\win32.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\win32_registry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\flutter_local_notifications_platform_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\notifications_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\model\\hint.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\lib\\package_info_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\lib\\method_channel_package_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\src\\file_version_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\backends\\local.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\blend\\blend.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\contrast\\contrast.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dislike\\dislike_analyzer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\dynamic_color.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\material_dynamic_colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\cam16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\hct.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\viewing_conditions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\palettes\\core_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\palettes\\tonal_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_celebi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_wsmeans.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_wu.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\dynamic_scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_content.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_expressive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_fidelity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_monochrome.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_neutral.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_tonal_spot.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_vibrant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\score\\score.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\temperature\\temperature_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\utils\\color_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\utils\\math_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\utils\\string_utils.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\extensions.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\physics.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.11.0\\lib\\meta_meta.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\date_symbol_data_custom.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\date_symbols.dart", "C:\\src\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_date_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\builders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\listenable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\pragma.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\run_guarded.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\auto_dispose_family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\async_notifier\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\auto_dispose_family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_notifier\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stack_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\common\\env.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\always_alive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\async_selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\provider_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\container.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\listen.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\proxy_provider_listenable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\ref.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\scheduler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\framework\\value_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\future_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\future_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\auto_dispose_family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\notifier\\family.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_notifier_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_notifier_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\state_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_provider\\auto_dispose.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\lib\\src\\stream_provider\\base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\lib\\src\\helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\lib\\src\\typedefs.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\lib\\clock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\callback_dispatcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\android\\method_channel_mappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\platform_specifics\\darwin\\mappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\lib\\src\\tz_datetime_mapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\async_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\async_memoizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\byte_collector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\cancelable_operation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\chunked_stream_reader.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\event_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\stream_consumer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\stream_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\delegate\\stream_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\future_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\lazy_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\null_stream_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\restartable_timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\single_subscription_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\sink_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_closer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_splitter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_subscription_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\subscription_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\typed_stream_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\algorithms.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\boollist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\canonicalized_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\comparators.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\functions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\iterable_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\iterable_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\list_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\priority_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\queue_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\union_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\union_set_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\unmodifiable_wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\lib\\equatable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\json_annotation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\lib\\freezed_annotation.g.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\error_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\app_error.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\providers\\error_provider.g.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\preference_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\welcome\\domain\\models\\welcome_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\notifications\\presentation\\providers\\notification_evaluator_provider.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\permission_handler_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\file_picker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\platform_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\file_picker_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\csv_settings_autodetection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\src\\complex_converter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\src\\csv_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\csv_to_list_converter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\list_to_csv_converter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\src\\dir_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\src\\document_tree.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\src\\exceptions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\src\\extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\src\\save_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\media_store_platform_interface.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\backup\\backup_errors.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\data_management\\domain\\conflict_resolver.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_paypal-0.2.0\\lib\\src\\screens\\complete_payment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\flutter_spinkit.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.8.0\\lib\\webview_flutter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_paypal-0.2.0\\lib\\src\\PaypalServices.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_paypal-0.2.0\\lib\\src\\errors\\network_error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\date_format_internal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_computation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\regexp.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\string_stack.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_format_field.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\number_symbols.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\number_symbols_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\number_format_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\number_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\compact_number_format.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\domain\\models\\alert_state_manager_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\alerts\\domain\\models\\alert_state_manager_state.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher_string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\type_conversion.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\services\\date_formatter_service.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fading_edge_scrollview-3.0.0\\lib\\fading_edge_scrollview.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\entries\\domain\\models\\entry_state.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\widgets\\custom_date_picker_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\async_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\change_notifier_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\consumer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\listenable_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\proxy_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\reassemble_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\value_listenable_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\domain\\models\\cost_mode.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\domain\\models\\date_range.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\models\\chart_data_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\providers\\cost_provider.g.dart", "D:\\000.Workspace\\Lekky\\lib\\features\\cost\\presentation\\widgets\\cost_trend_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.4\\lib\\platform.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\factory_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\database_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\mixin\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\mixin\\factory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\sql.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\open_options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\transaction.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\sqflite_debug.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\sql_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\database_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\platform\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\dev_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\method_channel_url_launcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\platform_navigation_delegate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\platform_webview_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\platform_webview_cookie_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\platform_webview_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\webview_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-3.16.3\\lib\\src\\android_webview.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\foundation\\foundation_api_impls.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\web_kit\\web_kit_api_impls.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\ui_kit\\ui_kit_api_impls.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\allocation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\arena.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\utf16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\utf8.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\bstr.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\callbacks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\constants_metadata.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\constants_nodoc.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\enums.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\exceptions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\guid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\inline.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\macros.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\propertykey.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\structs.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\winmd_constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\winrt_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\dialogs.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\int_to_hexstring.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\list_to_blob.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\set_ansi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\set_string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\set_string_array.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\extensions\\unpack_utf16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\advapi32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\bluetoothapis.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\bthprops.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\comctl32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\comdlg32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\crypt32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\dbghelp.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\dwmapi.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\dxva2.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\gdi32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\iphlpapi.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\kernel32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\magnification.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\netapi32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\ntdll.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\ole32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\oleaut32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\powrprof.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\rometadata.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\scarddlg.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\setupapi.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\shell32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\shlwapi.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\user32.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\uxtheme.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\version.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\winmm.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\winscard.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\winspool.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\wlanapi.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\xinput1_4.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_apiquery_l2_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_comm_l1_1_1.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_comm_l1_1_2.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_handle_l1_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_sysinfo_l1_2_3.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_winrt_l1_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_winrt_error_l1_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_core_winrt_string_l1_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_ro_typeresolution_l1_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_ro_typeresolution_l1_1_1.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_shcore_scaling_l1_1_1.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\win32\\api_ms_win_wsl_api_l1_1_0.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\combase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iagileobject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iapplicationactivationmanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxfactory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxfile.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxfilesenumerator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestapplication.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestapplicationsenumerator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestospackagedependency.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestpackagedependenciesenumerator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestpackagedependency.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestpackageid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestproperties.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader6.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxmanifestreader7.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iappxpackagereader.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudiocaptureclient.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclient.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclient2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclient3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclientduckingcontrol.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclock2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudioclockadjustment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudiorenderclient.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudiosessioncontrol.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudiosessionmanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iaudiostreamvolume.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ibindctx.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ichannelaudiovolume.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iclassfactory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iconnectionpoint.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iconnectionpointcontainer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\idesktopwallpaper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\idispatch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumidlist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienummoniker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumnetworkconnections.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumnetworks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumresources.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumspellingerror.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumstring.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumvariant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ienumwbemclassobject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ierrorinfo.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ifiledialog.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ifiledialog2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ifiledialogcustomize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ifileisinuse.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ifileopendialog.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ifilesavedialog.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iinitializewithwindow.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iinspectable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iknownfolder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iknownfoldermanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadataassemblyimport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadatadispenser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadatadispenserex.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadataimport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadataimport2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadatatables.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imetadatatables2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\immdevice.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\immdevicecollection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\immdeviceenumerator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\immendpoint.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\immnotificationclient.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imodalwindow.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\imoniker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\inetwork.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\inetworkconnection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\inetworklistmanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\inetworklistmanagerevents.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ipersist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ipersistfile.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ipersistmemory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ipersiststream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ipropertystore.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iprovideclassinfo.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\irestrictederrorinfo.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\irunningobjecttable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isensor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isensorcollection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isensordatareport.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isensormanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isequentialstream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellfolder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellitem.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellitem2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellitemarray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellitemfilter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellitemimagefactory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellitemresources.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishelllink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishelllinkdatalist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishelllinkdual.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ishellservice.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isimpleaudiovolume.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechaudioformat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechbasestream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechobjecttoken.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechobjecttokens.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechvoice.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechvoicestatus.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeechwaveformatex.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispellchecker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispellchecker2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispellcheckerchangedeventhandler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispellcheckerfactory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispellingerror.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispeventsource.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispnotifysource.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ispvoice.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\istream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\isupporterrorinfo.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\itypeinfo.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomation2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomation3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomation4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomation5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomation6.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationandcondition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationannotationpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationboolcondition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationcacherequest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationcondition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationcustomnavigationpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationdockpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationdragpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationdroptargetpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement6.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement7.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement8.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelement9.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationelementarray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationexpandcollapsepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationgriditempattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationgridpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationinvokepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationitemcontainerpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationlegacyiaccessiblepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationmultipleviewpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationnotcondition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationobjectmodelpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationorcondition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationpropertycondition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationproxyfactory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationproxyfactoryentry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationproxyfactorymapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationrangevaluepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationscrollitempattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationscrollpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationselectionitempattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationselectionpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationselectionpattern2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationspreadsheetitempattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationspreadsheetpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationstylespattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationsynchronizedinputpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtableitempattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtablepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextchildpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtexteditpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextpattern2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextrange.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextrange2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextrange3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtextrangearray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtogglepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtransformpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtransformpattern2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationtreewalker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationvaluepattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationvirtualizeditempattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuiautomationwindowpattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iunknown.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iuri.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\ivirtualdesktopmanager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemclassobject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemconfigurerefresher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemcontext.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemhiperfenum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemlocator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemobjectaccess.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemrefresher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwbemservices.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwebauthenticationcoremanagerinterop.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.5.0\\lib\\src\\com\\iwinhttprequest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\models\\models.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\registry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\registry_key.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\registry_value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\dbus.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\dbus_wrapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\notification_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\platform_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\storage.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding\\forwarding_directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding\\forwarding_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding\\forwarding_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding\\forwarding_link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\error_codes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\backends\\local\\local_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\internal_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\parsed_path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\posix.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\url.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\windows.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\toggleable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\src\\contrast_curve.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\src\\hct_solver.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\src\\point_provider_lab.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\src\\point_provider.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\characters_impl.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\stack_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\lib\\src\\default.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\lib\\src\\clock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\typed\\stream_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\capture_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\capture_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\release_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\result\\release_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_transformer\\reject_errors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_transformer\\handler_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_transformer\\stream_transformer_wrapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\lib\\src\\stream_sink_transformer\\typed.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\empty_unmodifiable_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\lib\\src\\equatable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\lib\\src\\equatable_config.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\lib\\src\\equatable_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\allowed_keys_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\checked_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\enum_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_converter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_enum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_key.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_literal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_serializable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_value.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\error_state.freezed.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\models\\app_error.freezed.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\src\\method_channel\\method_channel_permission_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\src\\permission_handler_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\src\\permission_status.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\src\\permissions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\src\\service_status.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\file_picker_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\file_picker_macos.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\linux\\file_picker_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\windows\\file_picker_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\src\\csv_argument_errors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\media_store_plus-0.1.3\\lib\\media_store_method_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\chasing_dots.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\circle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\cube_grid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\dancing_square.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\double_bounce.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\dual_ring.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\fading_circle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\fading_cube.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\fading_four.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\fading_grid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\folding_cube.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\hour_glass.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\piano_wave.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\pouring_hour_glass.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\pouring_hour_glass_refined.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\pulse.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\pulsing_grid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\pumping_heart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\ring.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\ripple.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\rotating_circle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\rotating_plain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\spinning_circle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\spinning_lines.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\square_circle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\three_bounce.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\three_in_out.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\wandering_cubes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\wave.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\wave_spinner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.8.0\\lib\\src\\navigation_delegate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.8.0\\lib\\src\\webview_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.8.0\\lib\\src\\webview_cookie_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.8.0\\lib\\src\\webview_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\http.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_auth-1.0.4\\lib\\http_auth.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fading_edge_scrollview-3.0.0\\lib\\src\\fading_edge_scrollview.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\lib\\nested.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\deferred_inherited_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\devtool.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\inherited_provider.dart", "D:\\000.Workspace\\Lekky\\lib\\core\\utils\\cost_calculator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\fl_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.4\\lib\\src\\interface\\local_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.4\\lib\\src\\interface\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.4\\lib\\src\\testing\\fake_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.1.0+1\\lib\\synchronized.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\path_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\batch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\cursor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\collection_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\value_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\arg_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\mixin\\import_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\logger\\sqflite_logger.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\platform\\platform_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\http_auth_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\http_response_error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\javascript_console_message.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\javascript_dialog_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\javascript_log_level.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\javascript_message.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\javascript_mode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\load_request_params.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\navigation_decision.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\navigation_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\platform_navigation_delegate_creation_params.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\platform_webview_controller_creation_params.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\platform_webview_cookie_manager_creation_params.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\platform_webview_permission_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\platform_webview_widget_creation_params.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\scroll_position_change.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\url_change.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\web_resource_error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\web_resource_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\web_resource_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\webview_cookie.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\lib\\src\\types\\webview_credential.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.14.0\\lib\\src\\common\\web_kit.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\models\\access_rights.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\models\\pointer_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\models\\registry_hive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\models\\registry_key_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\models\\registry_value_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.3\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_address.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_auth_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_introspect.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_method_call.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_method_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_object.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_remote_object.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_remote_object_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_server.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_signal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\posix.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\lib\\src\\file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\interface\\error_codes_dart_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\backends\\local\\local_directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\backends\\local\\local_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\backends\\local\\local_link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\table.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\breaks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\chain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\frame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\unparsed_frame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\lib\\src\\stopwatch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\lib\\src\\equatable_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_serializable.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.2.3\\lib\\src\\method_channel\\utils\\codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\linux\\dialog_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\exceptions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\windows\\file_picker_windows_ffi_types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_spinkit-5.2.1\\lib\\src\\tweens\\delay_tween.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\streamed_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\base_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\base_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\base_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\byte_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\multipart_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\multipart_request.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\streamed_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_auth-1.0.4\\lib\\src\\http_auth_basic.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_auth-1.0.4\\lib\\src\\http_auth_digest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_auth-1.0.4\\lib\\src\\http_auth_negotiate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\bar_chart\\bar_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\bar_chart\\bar_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\axis_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\axis_chart_widgets.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\base_chart\\base_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\base_chart\\fl_touch_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\line_chart\\line_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\line_chart\\line_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\pie_chart\\pie_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\pie_chart\\pie_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\radar_chart\\radar_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\radar_chart\\radar_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\scatter_chart\\scatter_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\scatter_chart\\scatter_chart_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.1.0+1\\lib\\src\\basic_lock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.1.0+1\\lib\\src\\reentrant_lock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\sql_command.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\env_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\sqflite_logger.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.4\\lib\\src\\database_file_system_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_uuid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getsid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getuid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_bus_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_error_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_interface_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_introspectable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_match_rule.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_member_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_message.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_object_manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_object_tree.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_peer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_properties.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_read_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_write_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\xml.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_auth_server.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\common.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.0\\lib\\src\\backends\\local\\local_file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\lazy_chain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\stack_zone_specification.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\lazy_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\lib\\src\\vm_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\linux\\kdialog_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-5.5.0\\lib\\src\\linux\\qarma_and_zenity_handler.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\io_client.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\http_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\multipart_file_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\boundary_characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_auth-1.0.4\\lib\\src\\http_auth_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\bar_chart\\bar_chart_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\axis_chart_scaffold_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\bar_chart\\bar_chart_helper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\utils\\lerp.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\utils\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\color_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\axis_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\border_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\base_chart\\base_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\line_chart\\line_chart_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\line_chart\\line_chart_helper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\gradient_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\pie_chart\\pie_chart_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\radar_chart\\radar_chart_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\radar_chart\\radar_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\scatter_chart\\scatter_chart_renderer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\scatter_chart\\scatter_chart_helper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.1.0+1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getsid_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\getuid_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\lib\\src\\dbus_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\default_mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\entity_mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\null_mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\enums\\attribute_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\enums\\node_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\format_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\parent_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\parser_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\tag_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\exceptions\\type_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\ancestors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\comparison.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\descendants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\find.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\following.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\mutator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\nodes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\preceding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\sibling.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\extensions\\string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_attributes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_children.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_visitor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_writer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\attribute.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\cdata.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\comment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\declaration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\doctype.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\document.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\document_fragment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\node.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\processing.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\text.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\token.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\normalizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\pretty_writer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\visitor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\visitors\\writer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\lib\\src\\io_streamed_response.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\authentication_challenge.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\case_insensitive_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\http_date.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\media_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\convert.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\crypto.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\base_chart\\render_base_chart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\utils\\canvas_wrapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\bar_chart\\bar_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\fl_titles_data_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\side_titles\\side_titles_widget.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\utils\\list_wrapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\axis_chart_helper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\line_chart\\line_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\paint_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\pie_chart\\pie_chart_helper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\pie_chart\\pie_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\radar_chart\\radar_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\scatter_chart\\scatter_chart_painter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\dtd\\external_id.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\nodes\\data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\namespace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\entities\\named_entities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\core.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\name_matcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\node_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\predicate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\xml_events.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\mixins\\has_value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\prefix_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\simple_name.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\string_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\scan.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding\\decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding\\encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\accumulator_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\byte_accumulator_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\codepage.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\fixed_datetime_formatter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\hex.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\identity_codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\percent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\string_accumulator_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\digest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\hash.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\hmac.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\md5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha1.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha256.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha512.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\path_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\rrect_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\bar_chart_data_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\side_titles_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\edge_insets_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\fl_border_data_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\side_titles\\side_titles_flex.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\axis_chart\\axis_chart_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\extensions\\text_align_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\chart\\base\\line.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\core\\context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\core\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\core\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\core\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\core\\token.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\codec\\event_codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\codec\\node_codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\event_decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\event_encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\node_decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\converters\\node_encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\cdata.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\comment.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\declaration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\doctype.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\end_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\processing.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\start_element.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\events\\text.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\each_event.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\flatten.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\normalizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\subtree_selector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\streams\\with_parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\event_attribute.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\visitor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\line_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\span_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\string_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\source_span.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\typed_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding\\charcodes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\hex\\decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\hex\\encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\percent\\decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\percent\\encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\digest_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\hash_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha512_fastsinks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-0.55.2\\lib\\src\\utils\\path_drawing\\dash_path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\shared\\annotations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\matches.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\token.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\misc\\newline.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\has_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\has_location.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\has_parent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\annotations\\annotator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\petitparser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\conversion_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\list_converter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml_events\\utils\\named.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\charcode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\eager_span_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\relative_span_scanner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\location.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\location_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span_mixin.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span_with_context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\src\\typed_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\typed_buffers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.1\\lib\\src\\charcodes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\matches\\matches_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\delegate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\definition.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\expression.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\matcher.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\lib\\src\\xml\\utils\\character_data_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\term_glyph.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\highlighter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\src\\typed_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\matches\\matches_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\definition\\grammar.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\definition\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\definition\\reference.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\definition\\resolve.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\expression\\builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\expression\\group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\accept.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\cast.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\cast_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\continuation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\flatten.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\permute.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\pick.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\trimming.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\action\\where.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\any_of.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\char.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\digit.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\letter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\lowercase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\none_of.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\predicate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\range.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\uppercase.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\whitespace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\word.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\and.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\choice.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\not.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\optional.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\sequence.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\settable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\skip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\misc\\eof.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\misc\\epsilon.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\misc\\failure.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\misc\\label.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\misc\\position.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\predicate\\any.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\predicate\\character.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\predicate\\pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\predicate\\predicate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\predicate\\string.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\character.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\greedy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\lazy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\limited.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\possessive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\repeating.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\separated.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\separated_by.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\repeater\\unbounded.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\utils\\failure_joiner.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\utils\\labeled.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\utils\\resolvable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\utils\\separated_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\ascii_glyph_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\glyph_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\unicode_glyph_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\top_level.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\charcode.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\definition\\internal\\reference.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\definition\\internal\\undefined.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\reflection\\iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\expression\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\expression\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\pattern\\parser_pattern.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\shared\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\utils\\sequential.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\code.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\optimize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\not.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\constant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_6.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_7.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_8.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\combinator\\generated\\sequence_9.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\pattern\\parser_match.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\pattern\\pattern_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\parser\\character\\lookup.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\lib\\src\\matcher\\pattern\\pattern_iterator.dart"], "outputs": ["D:\\000.Workspace\\Lekky\\.dart_tool\\flutter_build\\e13dd4fbbc7a11d1beb4a67b3b49c532\\app.dill"]}