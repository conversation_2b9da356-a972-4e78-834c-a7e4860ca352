import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../models/validation_issue.dart';

/// Service for dismissing missing entry validation issues
class MissingEntryDismissalService {
  final MeterReadingRepository _meterReadingRepository;

  /// Constructor
  MissingEntryDismissalService(this._meterReadingRepository);

  /// Dismiss a missing entry issue by creating dismissal entries every 30 days
  Future<List<MeterReading>> dismissMissingEntryIssue(
      ValidationIssue issue) async {
    try {
      final startDate = DateTime.parse(issue.metadata!['start_date']);
      final endDate = DateTime.parse(issue.metadata!['end_date']);
      final gapDays = issue.metadata!['gap_days'] as int;

      Logger.info(
          'MissingEntryDismissalService: Dismissing gap from $startDate to $endDate ($gapDays days)');

      final dismissalEntries = <MeterReading>[];

      // Create entries every 30 days within the gap
      DateTime currentDate = startDate.add(const Duration(days: 1));

      while (currentDate.isBefore(endDate)) {
        final dismissalEntry = MeterReading(
          value: 0.0, // Zero value for dismissal entries
          date: currentDate,
          status: EntryStatus.ignored,
          notes:
              'Dismissed missing entry gap: $gapDays days (${_formatDate(startDate)} - ${_formatDate(endDate)})',
        );

        dismissalEntries.add(dismissalEntry);
        Logger.info(
            'MissingEntryDismissalService: Created dismissal entry for $currentDate');

        // Move to next 30-day interval
        currentDate = currentDate.add(const Duration(days: 30));
      }

      // Save all dismissal entries
      for (final entry in dismissalEntries) {
        final id = await _meterReadingRepository.addMeterReading(entry);
        Logger.info(
            'MissingEntryDismissalService: Saved dismissal entry with ID $id');
      }

      Logger.info(
          'MissingEntryDismissalService: Successfully created ${dismissalEntries.length} dismissal entries');

      return dismissalEntries;
    } catch (e) {
      Logger.error(
          'MissingEntryDismissalService: Error dismissing missing entry issue: $e');
      rethrow;
    }
  }

  /// Check if a gap period has been dismissed
  bool isGapDismissed(
      List<MeterReading> readings, DateTime start, DateTime end) {
    return readings.any((reading) =>
        reading.status == EntryStatus.ignored &&
        reading.date.isAfter(start) &&
        reading.date.isBefore(end));
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
