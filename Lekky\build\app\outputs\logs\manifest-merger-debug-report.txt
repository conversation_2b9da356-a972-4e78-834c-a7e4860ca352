-- Merging decision tree log ---
application
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:22:5-70:19
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-14:19
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-14:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
MERGED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:flutter_plugin_android_lifecycle] D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:sqflite] D:\000.Workspace\Lekky\build\sqflite\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-12:12
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-16:12
MERGED from [:path_provider_android] D:\000.Workspace\Lekky\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_native_splash] D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:webview_flutter_android] D:\000.Workspace\Lekky\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:shared_preferences_android] D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:permission_handler_android] D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:integration_test] D:\000.Workspace\Lekky\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:7:1-14:12
MERGED from [:package_info_plus] D:\000.Workspace\Lekky\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:device_info_plus] D:\000.Workspace\Lekky\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:workmanager] D:\000.Workspace\Lekky\build\workmanager\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\efe66acca077e381e7402627a8481a6c\transformed\jetified-window-java-1.0.0-beta04\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c275d7683859a49fefc7284389acf42\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f3949bbe11b9a3df8476b3579cc79b0\transformed\browser-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4dc27c61ee84eee1f2bc3b392f54f7b\transformed\webkit-1.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c9c6c0686ab3bce033de91f8d70dd33\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f69110c2018cc9cf9a57f02713e4eeec\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6852fec6147a52fece3d18438463e6f2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\49edb8897109f34b15ded3962879d019\transformed\jetified-activity-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\758de6cd5027d52fe2d7978841275e6f\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f609bceb2779aea0408631e4886c8a03\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9446d4f8a39f1a78b33ccdd1b6fd8f3\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f381f9c43e7c7a1f9f8899ba1e8df6e\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11678733cf5264acd46ff6e099c1ea97\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c67993d1fcabd976b5acb98e08c8466\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5389d6eda3e5eba8ba922c509419fb7d\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1019a446298560e7a35814e77804afc7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e504bdcb092e95d1061c167d203844f3\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f6c5bd5440b448d26a4e361f1020005\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa78dce60caeb51e2766913f267c0ea5\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9123eda1df575eaec0a8647745461e77\transformed\lifecycle-livedata-core-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2a1be298aba77a6ae8756a7d54c1db1\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\61699763fb13d553040ed8f9e206d838\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2b841e27c844a77e2a1f20283ae4bd2\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\60b048a3937f3ed8abf2c4d3ce5e63c0\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2b69fb838fe869d82b2e9a10b81124f\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\099004d438a4cf8adb02af0ae2269356\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:1:1-7:12
	package
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:2:5-51
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-95:12
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-77
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-77
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-80
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-67
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:5-76
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:5-14:38
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-80
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-80
	android:maxSdkVersion
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:9-35
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:13:9-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:15:5-17:38
	android:maxSdkVersion
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:9-65
queries
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:77:5-94:15
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:78:9-81:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:79:13-72
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:79:21-70
data
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:80:13-50
	android:mimeType
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:80:19-48
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:83:9-85:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:84:13-79
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:84:21-76
intent#action:name:android.intent.action.CREATE_DOCUMENT+data:mimeType:text/csv
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:9-89:18
action#android.intent.action.CREATE_DOCUMENT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:87:13-76
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:87:21-73
intent#action:name:android.intent.action.OPEN_DOCUMENT+data:mimeType:text/csv
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:90:9-93:18
action#android.intent.action.OPEN_DOCUMENT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:91:13-74
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:91:21-71
uses-sdk
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:sqflite] D:\000.Workspace\Lekky\build\sqflite\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:sqflite] D:\000.Workspace\Lekky\build\sqflite\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:path_provider_android] D:\000.Workspace\Lekky\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:path_provider_android] D:\000.Workspace\Lekky\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_native_splash] D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_native_splash] D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:webview_flutter_android] D:\000.Workspace\Lekky\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:webview_flutter_android] D:\000.Workspace\Lekky\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:shared_preferences_android] D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:shared_preferences_android] D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:permission_handler_android] D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:permission_handler_android] D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:integration_test] D:\000.Workspace\Lekky\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-12:41
MERGED from [:integration_test] D:\000.Workspace\Lekky\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-12:41
MERGED from [:package_info_plus] D:\000.Workspace\Lekky\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:package_info_plus] D:\000.Workspace\Lekky\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:device_info_plus] D:\000.Workspace\Lekky\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:device_info_plus] D:\000.Workspace\Lekky\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:workmanager] D:\000.Workspace\Lekky\build\workmanager\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:workmanager] D:\000.Workspace\Lekky\build\workmanager\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.window:window-java:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\efe66acca077e381e7402627a8481a6c\transformed\jetified-window-java-1.0.0-beta04\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window-java:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\efe66acca077e381e7402627a8481a6c\transformed\jetified-window-java-1.0.0-beta04\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c275d7683859a49fefc7284389acf42\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c275d7683859a49fefc7284389acf42\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f3949bbe11b9a3df8476b3579cc79b0\transformed\browser-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f3949bbe11b9a3df8476b3579cc79b0\transformed\browser-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4dc27c61ee84eee1f2bc3b392f54f7b\transformed\webkit-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4dc27c61ee84eee1f2bc3b392f54f7b\transformed\webkit-1.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c9c6c0686ab3bce033de91f8d70dd33\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c9c6c0686ab3bce033de91f8d70dd33\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f69110c2018cc9cf9a57f02713e4eeec\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f69110c2018cc9cf9a57f02713e4eeec\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6852fec6147a52fece3d18438463e6f2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6852fec6147a52fece3d18438463e6f2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\49edb8897109f34b15ded3962879d019\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\49edb8897109f34b15ded3962879d019\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\758de6cd5027d52fe2d7978841275e6f\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\758de6cd5027d52fe2d7978841275e6f\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f609bceb2779aea0408631e4886c8a03\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f609bceb2779aea0408631e4886c8a03\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9446d4f8a39f1a78b33ccdd1b6fd8f3\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9446d4f8a39f1a78b33ccdd1b6fd8f3\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f381f9c43e7c7a1f9f8899ba1e8df6e\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f381f9c43e7c7a1f9f8899ba1e8df6e\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11678733cf5264acd46ff6e099c1ea97\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11678733cf5264acd46ff6e099c1ea97\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c67993d1fcabd976b5acb98e08c8466\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c67993d1fcabd976b5acb98e08c8466\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5389d6eda3e5eba8ba922c509419fb7d\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5389d6eda3e5eba8ba922c509419fb7d\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1019a446298560e7a35814e77804afc7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1019a446298560e7a35814e77804afc7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e504bdcb092e95d1061c167d203844f3\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e504bdcb092e95d1061c167d203844f3\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f6c5bd5440b448d26a4e361f1020005\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f6c5bd5440b448d26a4e361f1020005\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa78dce60caeb51e2766913f267c0ea5\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa78dce60caeb51e2766913f267c0ea5\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9123eda1df575eaec0a8647745461e77\transformed\lifecycle-livedata-core-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9123eda1df575eaec0a8647745461e77\transformed\lifecycle-livedata-core-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2a1be298aba77a6ae8756a7d54c1db1\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2a1be298aba77a6ae8756a7d54c1db1\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\61699763fb13d553040ed8f9e206d838\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\61699763fb13d553040ed8f9e206d838\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2b841e27c844a77e2a1f20283ae4bd2\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2b841e27c844a77e2a1f20283ae4bd2\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\60b048a3937f3ed8abf2c4d3ce5e63c0\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\60b048a3937f3ed8abf2c4d3ce5e63c0\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2b69fb838fe869d82b2e9a10b81124f\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2b69fb838fe869d82b2e9a10b81124f\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\099004d438a4cf8adb02af0ae2269356\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\099004d438a4cf8adb02af0ae2269356\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.INTERNET
ADDED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-66
	android:name
		ADDED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
	android:exported
		ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
	android:name
		ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35761e94c6d5258a9b643a9e38a6ce19\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
