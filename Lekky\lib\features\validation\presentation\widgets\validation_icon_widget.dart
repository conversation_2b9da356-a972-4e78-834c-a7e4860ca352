import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/validation_colors.dart';
import '../../domain/models/validation_issue.dart';
import '../providers/validation_provider.dart';

/// A reusable validation icon widget that shows validation status
///
/// Features:
/// - White icon when no validation issues exist
/// - Red icon for high severity issues
/// - Orange icon for medium severity issues
/// - Blue icon for low severity issues
/// - Smooth color transitions between states
/// - Loading indicator during validation
/// - Error state with inverted colors for validation failures
class ValidationIconWidget extends ConsumerStatefulWidget {
  /// Callback when the icon is tapped
  final VoidCallback onTap;

  /// Constructor
  const ValidationIconWidget({
    super.key,
    required this.onTap,
  });

  @override
  ConsumerState<ValidationIconWidget> createState() =>
      _ValidationIconWidgetState();
}

class _ValidationIconWidgetState extends ConsumerState<ValidationIconWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _colorAnimation;
  Color _currentColor = ValidationColors.noIssuesLight;
  List<Color> _severityColors = [];
  int _currentColorIndex = 0;
  bool _isMultiSeverityMode = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: ValidationColors.animationDuration,
      vsync: this,
    );
    _colorAnimation = ColorTween(
      begin: _currentColor,
      end: _currentColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: ValidationColors.animationCurve,
    ));

    // Listen for animation completion to cycle colors
    _animationController.addStatusListener(_onAnimationStatusChanged);
  }

  @override
  void dispose() {
    _animationController.removeStatusListener(_onAnimationStatusChanged);
    _animationController.dispose();
    super.dispose();
  }

  /// Handle animation status changes for color cycling
  void _onAnimationStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed && _isMultiSeverityMode) {
      // Move to next color in the cycle
      _currentColorIndex = (_currentColorIndex + 1) % _severityColors.length;
      _startNextColorTransition();
    }
  }

  /// Start transition to the next color in the cycle
  void _startNextColorTransition() {
    if (_severityColors.isNotEmpty) {
      final nextColor = _severityColors[_currentColorIndex];
      _colorAnimation = ColorTween(
        begin: _currentColor,
        end: nextColor,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: ValidationColors.animationCurve,
      ));

      _currentColor = nextColor;
      _animationController.forward(from: 0.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    final validationState = ref.watch(validationControllerProvider);
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return _buildIcon(context, validationState, isDarkTheme);
  }

  /// Build the appropriate icon based on validation state
  Widget _buildIcon(
      BuildContext context, ValidationState state, bool isDarkTheme) {
    // Handle loading state with loading indicator
    if (state.isLoading) {
      return Stack(
        children: [
          // Base icon (dimmed)
          IconButton(
            icon: Icon(
              Icons.check_circle,
              color: Colors.white.withOpacity(0.5),
              size: 28,
            ),
            onPressed: widget.onTap,
            tooltip: 'Entry Validation (Loading...)',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          // Loading indicator overlay
          const Positioned(
            right: 0,
            top: 0,
            child: SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        ],
      );
    }

    // Handle error state with inverted colors
    if (state.errorMessage != null) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.black, width: 2),
        ),
        child: IconButton(
          icon: const Icon(
            Icons.error_outline,
            color: Colors.black,
            size: 24,
          ),
          onPressed: widget.onTap,
          tooltip: 'Entry Validation (Error)',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      );
    }

    // Determine icon color and animation mode based on validation severity
    _updateValidationDisplay(state, isDarkTheme);

    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return IconButton(
          icon: Icon(
            Icons.check_circle,
            color: _colorAnimation.value ?? _currentColor,
            size: 28,
          ),
          onPressed: widget.onTap,
          tooltip: _getTooltipText(state),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        );
      },
    );
  }

  /// Update validation display based on issues and severity types
  void _updateValidationDisplay(ValidationState state, bool isDarkTheme) {
    if (state.integrityReport?.issues == null ||
        state.integrityReport!.issues.isEmpty) {
      // No issues - solid white, no animation
      _stopMultiSeverityAnimation();
      final noIssuesColor = isDarkTheme
          ? ValidationColors.noIssuesDark
          : ValidationColors.noIssuesLight;
      _setStaticColor(noIssuesColor);
      return;
    }

    final issues = state.integrityReport!.issues;
    final severityTypes = _getUniqueSeverityTypes(issues, isDarkTheme);

    if (severityTypes.length == 1) {
      // Single severity type - solid color, no animation
      _stopMultiSeverityAnimation();
      _setStaticColor(severityTypes.first);
    } else {
      // Multiple severity types - animated color cycling with white
      _startMultiSeverityAnimation(severityTypes, isDarkTheme);
    }
  }

  /// Get unique severity colors from issues
  List<Color> _getUniqueSeverityTypes(
      List<ValidationIssue> issues, bool isDarkTheme) {
    final severities = <ValidationIssueSeverity>{};
    for (final issue in issues) {
      severities.add(issue.severity);
    }

    final colors = <Color>[];
    for (final severity in severities) {
      colors.add(ValidationColors.getColorForSeverity(severity,
          isDarkTheme: isDarkTheme));
    }

    return colors;
  }

  /// Set static color without animation
  void _setStaticColor(Color color) {
    if (_currentColor != color) {
      _currentColor = color;
      _colorAnimation = ColorTween(
        begin: color,
        end: color,
      ).animate(_animationController);
      setState(() {});
    }
  }

  /// Start multi-severity animation with color cycling
  void _startMultiSeverityAnimation(
      List<Color> severityColors, bool isDarkTheme) {
    final whiteColor = isDarkTheme
        ? ValidationColors.noIssuesDark
        : ValidationColors.noIssuesLight;

    // Create color sequence: severity colors + white for attention
    _severityColors = [];
    for (final color in severityColors) {
      _severityColors.add(color);
      _severityColors.add(whiteColor); // Add white between each severity color
    }

    if (!_isMultiSeverityMode || _severityColors != _severityColors) {
      _isMultiSeverityMode = true;
      _currentColorIndex = 0;
      _startNextColorTransition();
    }
  }

  /// Stop multi-severity animation
  void _stopMultiSeverityAnimation() {
    if (_isMultiSeverityMode) {
      _isMultiSeverityMode = false;
      _animationController.stop();
      _severityColors.clear();
    }
  }

  /// Get tooltip text based on validation state
  String _getTooltipText(ValidationState state) {
    if (state.integrityReport?.issues == null ||
        state.integrityReport!.issues.isEmpty) {
      return ValidationColors.getTooltipText(null, 0);
    }

    final issues = state.integrityReport!.issues;
    final highestSeverity = ValidationColors.getHighestSeverity(issues);
    return ValidationColors.getTooltipText(highestSeverity, issues.length);
  }
}
