import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/validation_colors.dart';
import '../../domain/models/validation_issue.dart';
import '../providers/validation_provider.dart';

/// A reusable validation icon widget that shows validation status
///
/// Features:
/// - White icon when no validation issues exist
/// - Red icon for high severity issues
/// - Orange icon for medium severity issues
/// - Blue icon for low severity issues
/// - Smooth color transitions between states
/// - Loading indicator during validation
/// - Error state with inverted colors for validation failures
class ValidationIconWidget extends ConsumerStatefulWidget {
  /// Callback when the icon is tapped
  final VoidCallback onTap;

  /// Constructor
  const ValidationIconWidget({
    super.key,
    required this.onTap,
  });

  @override
  ConsumerState<ValidationIconWidget> createState() =>
      _ValidationIconWidgetState();
}

class _ValidationIconWidgetState extends ConsumerState<ValidationIconWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _colorAnimation;
  Color _currentColor = ValidationColors.noIssuesLight;
  List<Color> _severityColors = [];
  int _currentColorIndex = 0;
  bool _isMultiSeverityMode = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: ValidationColors.animationDuration,
      vsync: this,
    );
    _colorAnimation = ColorTween(
      begin: _currentColor,
      end: _currentColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: ValidationColors.animationCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final validationState = ref.watch(validationControllerProvider);
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return _buildIcon(context, validationState, isDarkTheme);
  }

  /// Build the appropriate icon based on validation state
  Widget _buildIcon(
      BuildContext context, ValidationState state, bool isDarkTheme) {
    // Handle loading state with loading indicator
    if (state.isLoading) {
      return Stack(
        children: [
          // Base icon (dimmed)
          IconButton(
            icon: Icon(
              Icons.check_circle,
              color: Colors.white.withOpacity(0.5),
              size: 28,
            ),
            onPressed: widget.onTap,
            tooltip: 'Entry Validation (Loading...)',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          // Loading indicator overlay
          const Positioned(
            right: 0,
            top: 0,
            child: SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        ],
      );
    }

    // Handle error state with inverted colors
    if (state.errorMessage != null) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.black, width: 2),
        ),
        child: IconButton(
          icon: const Icon(
            Icons.error_outline,
            color: Colors.black,
            size: 24,
          ),
          onPressed: widget.onTap,
          tooltip: 'Entry Validation (Error)',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      );
    }

    // Determine icon color based on validation severity with animation
    final targetColor = _getIconColor(state, isDarkTheme);
    _updateColorAnimation(targetColor);

    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return IconButton(
          icon: Icon(
            Icons.check_circle,
            color: _colorAnimation.value ?? targetColor,
            size: 28,
          ),
          onPressed: widget.onTap,
          tooltip: _getTooltipText(state),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        );
      },
    );
  }

  /// Get icon color based on validation severity
  Color _getIconColor(ValidationState state, bool isDarkTheme) {
    if (state.integrityReport?.issues == null ||
        state.integrityReport!.issues.isEmpty) {
      return isDarkTheme
          ? ValidationColors.noIssuesDark
          : ValidationColors.noIssuesLight;
    }

    final highestSeverity =
        ValidationColors.getHighestSeverity(state.integrityReport!.issues);
    return ValidationColors.getColorForSeverity(highestSeverity,
        isDarkTheme: isDarkTheme);
  }

  /// Update color animation when target color changes
  void _updateColorAnimation(Color targetColor) {
    if (_currentColor != targetColor) {
      _colorAnimation = ColorTween(
        begin: _currentColor,
        end: targetColor,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: ValidationColors.animationCurve,
      ));

      _currentColor = targetColor;
      _animationController.forward(from: 0.0);
    }
  }

  /// Get tooltip text based on validation state
  String _getTooltipText(ValidationState state) {
    if (state.integrityReport?.issues == null ||
        state.integrityReport!.issues.isEmpty) {
      return ValidationColors.getTooltipText(null, 0);
    }

    final issues = state.integrityReport!.issues;
    final highestSeverity = ValidationColors.getHighestSeverity(issues);
    return ValidationColors.getTooltipText(highestSeverity, issues.length);
  }
}
